#!/usr/bin/env python
import sys
import warnings
import argparse
from datetime import datetime
import os
import json
import importlib.util
import pathlib
import re

# 添加当前模块路径到sys.path
current_dir = pathlib.Path(__file__).parent.absolute()
parent_dir = current_dir.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

from kid_link_ai.crew import CoursewareGenerator
from kid_link_ai.tools import MediaProcessorTool
from kid_link_ai.save_courseware import save_courseware_content, get_unique_id, create_output_directory, create_info_file

warnings.filterwarnings("ignore", category=SyntaxWarning, module="pysbd")

# This main file is intended to be a way for you to run your
# crew locally, so refrain from adding unnecessary logic into this file.
# Replace with inputs you want to test with, it will automatically
# interpolate any tasks and agents information

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='生成小学课件的AI应用')
    parser.add_argument('--subject', type=str, default='语文', help='课程科目，如语文、数学、英语等')
    parser.add_argument('--grade', type=str, default='一年级', help='年级，如一年级、二年级等')
    parser.add_argument('--textbook_version', type=str, default='人教版', help='教材版本，如人教版、北师大版等')
    parser.add_argument('--unit', type=str, default='礼貌用语', help='教学单元或主题')
    parser.add_argument('--process_media', action='store_true', help='是否处理媒体内容')
    parser.add_argument('--courseware_file', type=str, default='courseware.txt', help='课件文件路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出根目录')
    parser.add_argument('--save_output', action='store_true', help='是否将课件保存到唯一编号的目录中')
    return parser.parse_args()

def run():
    """
    运行课件生成crew
    """
    args = parse_args()
    
    inputs = {
        'subject': args.subject,
        'grade': args.grade,
        'textbook_version': args.textbook_version,
        'unit': args.unit,
        'current_year': str(datetime.now().year)
    }
    
    print(f"开始生成{args.grade}{args.subject}《{args.unit}》课件...")
    
    try:
        generator = CoursewareGenerator()
        crew = generator.create_crew(inputs=inputs)
        crew_id = crew.id
        print(crew_id)
        result = crew.kickoff()
        print(crew.usage_metrics)
        # 记录媒体信息文件路径
        media_info_file = None
        
        # 如果需要处理媒体内容
        if args.process_media:
            media_result = process_media(args.courseware_file, args.output_dir)
            # 尝试从结果中获取媒体信息文件路径
            try:
                media_result_dict = json.loads(media_result)
                if 'output_file' in media_result_dict:
                    media_info_file = media_result_dict['output_file']
            except:
                pass
        
        # 如果需要保存到唯一编号的目录
        if args.save_output:
            save_to_unique_directory(args.courseware_file, media_info_file, args.output_dir, result)
        
        return result
    except Exception as e:
        raise Exception(f"生成课件过程中出现错误: {e}")

def save_to_unique_directory(courseware_file, media_info_file, base_output_dir, crew_result):
    """
    将课件内容和媒体信息保存到唯一编号的目录
    
    Args:
        courseware_file: 课件文件路径
        media_info_file: 媒体信息文件路径
        base_output_dir: 输出根目录
        crew_result: CrewAI的执行结果
    """
    try:
        # 尝试从结果中获取crew_id
        crew_id = None
        if hasattr(crew_result, 'crew_id'):
            crew_id = crew_result.crew_id
        elif isinstance(crew_result, dict) and 'crew_id' in crew_result:
            crew_id = crew_result['crew_id']
        
        # 获取唯一ID
        unique_id = get_unique_id(crew_id)
        print(f"\n开始保存课件到唯一编号目录...")
        print(f"使用唯一ID：{unique_id}")
        
        # 创建输出目录
        output_dir = create_output_directory(base_output_dir, unique_id)
        print(f"创建输出目录：{output_dir}")
        
        # 保存课件内容
        success1 = save_courseware_content(courseware_file, output_dir)
        
        # 保存媒体信息
        success2 = False
        if media_info_file and os.path.exists(media_info_file):
            # 复制媒体信息文件到输出目录
            media_output = os.path.join(output_dir, 'media_info.json')
            with open(media_info_file, 'r', encoding='utf-8') as f_in:
                media_info = json.load(f_in)
                
            with open(media_output, 'w', encoding='utf-8') as f_out:
                json.dump(media_info, f_out, ensure_ascii=False, indent=2)
            
            print(f"媒体信息已保存到 {media_output}")
            success2 = True
            
            # 创建媒体资源目录
            media_dir = os.path.join(output_dir, 'media')
            os.makedirs(media_dir, exist_ok=True)
            
            # 如果有媒体项，记录媒体资源信息
            if 'media_items' in media_info and media_info['media_items']:
                print("处理媒体资源信息...")
                for item in media_info['media_items']:
                    filename = item.get('filename')
                    media_type = item.get('media_type')
                    if filename and media_type:
                        print(f"媒体项：{filename} ({media_type})")
        
        # 保存media_report（如果有）
        success3 = False
        media_report = None
        
        # 尝试从crew_result中提取media_report
        if hasattr(crew_result, 'media_report'):
            media_report = crew_result.media_report
        elif isinstance(crew_result, dict) and 'media_report' in crew_result:
            media_report = crew_result['media_report']
        # 检查最后一个任务的结果中是否包含media_report
        elif hasattr(crew_result, 'task_results') and crew_result.task_results:
            last_task_result = crew_result.task_results[-1]
            if isinstance(last_task_result, dict) and 'media_report' in last_task_result:
                media_report = last_task_result['media_report']
        # 从task_results数组中提取media_report
        elif isinstance(crew_result, dict) and 'task_results' in crew_result:
            task_results = crew_result['task_results']
            if isinstance(task_results, list) and len(task_results) > 0:
                # 检查最后一个任务
                last_task = task_results[-1]
                if isinstance(last_task, dict) and 'media_report' in last_task:
                    media_report = last_task['media_report']
                else:
                    # 如果最后一个任务没有media_report，遍历所有任务寻找
                    for task in reversed(task_results):
                        if isinstance(task, dict) and 'media_report' in task:
                            media_report = task['media_report']
                            break
        # 尝试从CrewAI结果的字符串表示中提取media_report
        elif hasattr(crew_result, '__str__'):
            result_str = str(crew_result)
            # 查找包含media_report的JSON结构
            import re
            # 改进的正则表达式，寻找完整的JSON结构
            # 首先查找包含"media_report"的行
            if '"media_report"' in result_str:
                # 尝试找到包含media_report的完整JSON块
                lines = result_str.split('\n')
                json_start = -1
                json_end = -1
                brace_count = 0
                
                # 查找包含media_report的行
                media_report_line = -1
                for i, line in enumerate(lines):
                    if '"media_report"' in line:
                        media_report_line = i
                        break
                
                if media_report_line != -1:
                    # 向前查找JSON开始的位置（查找最外层的大括号）
                    for j in range(media_report_line, -1, -1):
                        if '{' in lines[j]:
                            # 检查这一行是否是JSON的开始（通常前面只有空格）
                            stripped = lines[j].strip()
                            if stripped.startswith('{'):
                                json_start = j
                                break
                    
                    if json_start != -1:
                        # 从JSON开始位置计算大括号平衡
                        brace_count = 0
                        for i in range(json_start, len(lines)):
                            line = lines[i]
                            brace_count += line.count('{') - line.count('}')
                            
                            # 当大括号平衡为0且当前行包含}时，找到JSON结束
                            if brace_count == 0 and '}' in line and i >= json_start:
                                json_end = i
                                break
                        
                        if json_start != -1 and json_end != -1:
                            # 提取JSON文本
                            json_text = '\n'.join(lines[json_start:json_end+1])
                            try:
                                import json as json_module
                                parsed = json_module.loads(json_text)
                                if 'media_report' in parsed:
                                    media_report = parsed['media_report']
                            except json_module.JSONDecodeError:
                                # 如果解析失败，尝试清理文本后再解析
                                try:
                                    # 移除可能的注释和额外字符
                                    cleaned_text = re.sub(r'^\s*#+.*$', '', json_text, flags=re.MULTILINE)
                                    cleaned_text = re.sub(r'^\s*##.*$', '', cleaned_text, flags=re.MULTILINE)
                                    cleaned_text = cleaned_text.strip()
                                    parsed = json_module.loads(cleaned_text)
                                    if 'media_report' in parsed:
                                        media_report = parsed['media_report']
                                except json_module.JSONDecodeError:
                                    pass
        
        if media_report:
            media_report_file = os.path.join(output_dir, 'media_report.json')
            with open(media_report_file, 'w', encoding='utf-8') as f:
                if isinstance(media_report, str):
                    # 尝试解析JSON字符串
                    try:
                        media_report_json = json.loads(media_report)
                        json.dump(media_report_json, f, ensure_ascii=False, indent=2)
                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，直接写入字符串
                        f.write(media_report)
                else:
                    # 如果已经是字典或其他对象，转换为JSON
                    json.dump(media_report, f, ensure_ascii=False, indent=2)
            
            print(f"媒体处理报告已保存到 {media_report_file}")
            success3 = True
        
        # 创建信息文件
        success4 = create_info_file(output_dir, crew_id)
        
        if success1 or success2 or success3:
            print(f"课件内容已成功保存到目录：{output_dir}")
            return True
        else:
            print("保存课件内容失败")
            return False
    except Exception as e:
        print(f"保存到唯一编号目录时出错：{str(e)}")
        return False

def process_media(courseware_file='courseware.txt', output_dir='media_outputs'):
    """
    处理课件中的媒体内容
    
    Args:
        courseware_file: 课件文件路径
        output_dir: 媒体输出目录
    
    Returns:
        str: 处理结果
    """
    try:
        # 检查课件文件是否存在
        if not os.path.exists(courseware_file):
            raise FileNotFoundError(f"课件文件 {courseware_file} 不存在")
        
        # 读取课件内容
        with open(courseware_file, 'r', encoding='utf-8') as f:
            courseware_content = f.read()
        
        print(f"\n{'=' * 60}")
        print(f"开始处理课件媒体内容...")
        print(f"课件文件：{courseware_file}")
        print(f"输出目录：{output_dir}")
        print(f"{'-' * 60}")
        
        # 创建媒体处理工具
        media_processor = MediaProcessorTool()
        
        # 处理媒体内容
        result = media_processor._run(courseware_content, output_dir)
        result_dict = json.loads(result)
        
        # 输出处理结果
        status = result_dict.get('status', 'unknown')
        message = result_dict.get('message', '')
        media_count = result_dict.get('media_count', 0)
        
        print(f"\n处理状态：{status}")
        print(f"处理信息：{message}")
        print(f"媒体项数量：{media_count}")
        
        # 如果有媒体项，显示简要信息
        if media_count > 0 and 'media_items' in result_dict:
            print("\n媒体项简要信息：")
            for i, item in enumerate(result_dict['media_items']):
                print(f"[{i+1}] {item.get('filename', '未知文件')} - {item.get('media_type', '未知')} - {item.get('status', '未知')}")
                
                # 显示生成结果
                if 'generation_result' in item:
                    gen_result = item['generation_result']
                    
                    # 对于图片，显示URL
                    if item.get('media_type') == 'image' and 'image_url' in gen_result:
                        print(f"    图片URL：{gen_result['image_url']}")
                    
                    # 对于视频，显示任务ID
                    if item.get('media_type') == 'video' and 'task_id' in gen_result:
                        task_id = gen_result['task_id']
                        print(f"    视频任务ID：{task_id}")
                        print(f"    查询视频状态：python -m kid_link_ai.process_media --task_id {task_id}")
        
        # 如果有输出文件，显示路径
        if 'output_file' in result_dict:
            print(f"\n处理结果已保存到：{result_dict['output_file']}")
            print(f"查看详细信息：python -m kid_link_ai.process_media --courseware_file {courseware_file}")
        
        print(f"{'=' * 60}\n")
        return result
    except Exception as e:
        error_msg = f"处理媒体内容过程中出现错误: {e}"
        print(f"\n错误: {error_msg}")
        raise Exception(error_msg)

def train():
    """
    训练课件生成crew
    """
    args = parse_args()
    
    inputs = {
        'subject': args.subject,
        'grade': args.grade,
        'textbook_version': args.textbook_version,
        'unit': args.unit,
        'current_year': str(datetime.now().year)
    }
    
    try:
        CoursewareGenerator().crew().train(n_iterations=int(sys.argv[1]), filename=sys.argv[2], inputs=inputs)
    except Exception as e:
        raise Exception(f"训练过程中出现错误: {e}")

def replay():
    """
    重新执行crew的特定任务
    """
    try:
        CoursewareGenerator().crew().replay(task_id=sys.argv[1])
    except Exception as e:
        raise Exception(f"重新执行任务过程中出现错误: {e}")

def test():
    """
    测试crew的执行效果
    """
    args = parse_args()
    
    inputs = {
        'subject': args.subject,
        'grade': args.grade,
        'textbook_version': args.textbook_version,
        'unit': args.unit,
        'current_year': str(datetime.now().year)
    }
    
    try:
        CoursewareGenerator().crew().test(n_iterations=int(sys.argv[1]), eval_llm=sys.argv[2], inputs=inputs)
    except Exception as e:
        raise Exception(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    run()
