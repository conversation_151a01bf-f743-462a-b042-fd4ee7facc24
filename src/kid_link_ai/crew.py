from crewai import Agent, Crew, Process, Task
from .tools import (
    InteractiveFormatTool,
    CoursewareValidatorTool,
    CoursewareExampleTool,
    MediaProcessorTool
)
from .config.llm_config import llm_manager, agent_llm_mapper

class CoursewareGenerator:
    """课件生成AI系统"""
    
    def __init__(self):
        # 初始化LLM配置
        self._setup_llm_configs()
        
        # 创建agents
        self.curriculum_designer = Agent(
            role="小学课程设计专家",
            goal="根据给定的科目、教材版本和单元，设计高质量的小学课件内容",
            backstory="你是一位经验丰富的小学课程设计专家，精通各种教学方法和课件设计技巧。"
                  "你能够根据小学生的认知特点设计生动有趣的课程内容，帮助他们轻松掌握知识。"
                  "你特别擅长设计符合教育部标准的课件，确保教学内容既符合课程要求又能吸引小学生的注意力。",
            verbose=True,
            tools=[CoursewareExampleTool()],
            llm=agent_llm_mapper.get_agent_llm("小学课程设计专家")
        )
        
        self.interactive_designer = Agent(
            role="教育交互设计师",
            goal="为课件设计合适的交互元素、文字旁白和引导，提高课件的互动性和教学效果，确保交互格式符合机器人（App）的规范",
            backstory="你是一位专业的教育交互设计师，擅长设计适合机器人（App）使用的交互元素和文字旁白。"
                  "你了解各种交互方式（如选择题、连线、翻牌等）的特点和适用场景，能够为不同类型的教学内容选择最佳的交互形式。"
                  "你擅长编写生动有趣的文字旁白和引导，让课件更有趣味性，增强学生的参与感。"
                  "你了解机器人动作（如#摇头、#呲牙等）的使用方法，能够恰当地融入课件。"
                  "你的设计理念是通过有趣的交互和丰富的旁白引导增强学生的学习体验，提高他们的参与度和学习效果。"
                  "你非常注重交互格式的规范性，确保所有交互元素（除选择题外）都使用大括号 {} 包围选项，"
                  "多选题中必须标明哪些是正确选项（=正确选项N@图片.png）和错误选项（=错误选项N@图片.png）。",
            verbose=True,
            tools=[
                InteractiveFormatTool(),
                CoursewareExampleTool()
            ],
            llm=agent_llm_mapper.get_agent_llm("教育交互设计师")
        )
        
        self.content_integrator = Agent(
            role="课件内容整合专家",
            goal="整合课程内容、交互设计、文字旁白和机器人动作，生成完整、规范的课件文本，并处理课件中的媒体内容",
            backstory="你是一位擅长整合各类教育资源的专家，能够将课程内容、交互设计、文字旁白和机器人动作无缝融合。"
                  "你熟悉课件的格式规范，能够生成符合机器人（App）识别要求的课件文本。"
                  "你善于编写引人入胜的旁白和设计合理的机器人动作（如#摇头、#呲牙等），增强课件的生动性。"
                  "你关注课件的流畅性和教学节奏，确保生成的课件能够自然过渡、层次分明、易于理解，且富有教学引导性。"
                  "你还能够处理课件中的媒体内容，调用相应的生成工具来生成图片和视频。",
            verbose=True,
            tools=[
                CoursewareValidatorTool(),
                CoursewareExampleTool(),
                MediaProcessorTool()
            ],
            llm=agent_llm_mapper.get_agent_llm("课件内容整合专家")
        )
    
    def _setup_llm_configs(self):
        """设置LLM配置"""
        # 打印可用的LLM配置
        llm_manager.print_available_models()
        
        # 设置默认的Agent LLM映射
        agent_llm_mapper.setup_default_mapping()
        
        # 打印映射信息
        agent_llm_mapper.print_mapping_info()
    
    def create_tasks(self, subject, grade, textbook_version, unit, current_year):
        """创建任务"""
        # 课程设计任务
        curriculum_task = Task(
            description=f"""
            根据以下信息设计小学课件的主要内容：
            科目：{subject}
            教材版本：{textbook_version}
            单元：{unit}
            年级：{grade}
            当前年份：{current_year}
            
            请设计一份完整的课件内容，包括：
            1. 课程主题和教学目标
            2. 课程的主要知识点和技能点
            3. 教学内容的组织和呈现方式
            4. 适合小学生认知特点的教学方法
            
            内容要符合教育部标准，适合机器人（App）进行教学。
            """,
            expected_output="一份结构清晰的课件内容设计方案，包含主题、目标、知识点和教学流程。",
            agent=self.curriculum_designer
        )
        
        # 交互设计任务
        interaction_task = Task(
            description=f"""
            根据课程设计专家提供的课件内容，设计适合的交互元素、文字旁白和引导。
            
            【文字旁白和引导】：
            1. 为每个教学环节添加引人入胜的文字旁白，类似老师在课堂上的讲解
            2. 添加适当的开场白、过渡语和总结语
            3. 设计与小朋友互动的问题和鼓励语
            4. 添加适当的表扬和肯定语句
            
            【机器人动作】：
            使用#开头的命令控制机器人动作，如：
            1. #摇头 - 机器人摇头动作
            2. #呲牙 - 机器人呲牙表情
            3. #卖萌 - 机器人卖萌动作
            4. #坏笑 - 机器人坏笑表情
            5. #花痴 - 机器人花痴表情
            
            【交互类型】可以包括但不限于：
            1. 选择题（=选择）- 格式：=选择 问题@选项1.png @选项2.png ...
            2. 翻牌（=不重复选择）- 格式：=不重复选择 {{ =选项1@图片1.png 词条1 =选项2@图片2.png 词条2 ... }}
            3. 抓娃娃（=不重复选择）- 格式同上
            4. 砸金蛋（=不重复选择）- 格式同上
            5. 大转盘（=重复选择）- 格式：=重复选择*N {{ 内容说明 @背景音乐.mp3 }}
            6. 心灵感应（=重复选择）- 格式同上
            7. 连线（=连线）- 格式：=连线 标题 {{ =配对1@图片1.png@图片2.png =配对2@图片3.png@图片4.png ... }}
            8. 多选（=多选）- 格式：=多选 问题 {{ =正确选项1@图片1.png =正确选项2@图片2.png =错误选项1@图片3.png ... }}
            9. 展示（=展示）- 格式：=展示 说明文字
            
            【多媒体内容】：
            使用@符号引入多媒体内容，如：
            1. @背景音乐.mp3 - 播放背景音乐
            2. @图片.png - 显示图片
            3. @视频.mp4 - 播放视频
            4. 可以使用*N表示重复次数，如 @音乐.mp3 *5
            
            【点名互动】：
            使用{{点名}}进行点名互动
            
            【重要格式说明】:
            1. 除了选择题外，所有交互类型都必须使用大括号 {{ }} 包围选项内容
            2. 多选题必须使用=正确选项和=错误选项标记，且至少有一个正确选项
            3. 每个选项下方需要添加相应的解释，使用@图片.png 格式
            4. 可以使用+N表示停顿N秒，如"今天我们来学习礼貌用语+2"表示停顿2秒
            
            在适当的位置添加交互元素、文字旁白和机器人动作，提高课件的互动性和教学效果。
            每个交互元素需要说明：
            1. 交互类型
            2. 交互内容（问题和选项）
            3. 在课件中的位置
            4. 交互的教学目的
            """,
            expected_output="一份详细的交互设计方案，包含文字旁白、引导、机器人动作和互动元素。请确保每种交互类型都使用正确的格式，并提供足够的文字旁白和引导。",
            agent=self.interactive_designer,
            context=[curriculum_task]
        )
        
        # 内容整合任务
        content_task = Task(
            description=f"""
            将课程内容、交互设计、文字旁白和机器人动作整合成一个完整的课件文本。
            课件文本需要符合机器人（App）识别的格式规范：
            
            【文本内容要求】：
            1. 包含充分的文字旁白和引导，类似老师在课堂上的讲解
            2. 有明确的开场白、教学内容和结束语
            3. 提供充分的引导和提示，帮助学生理解和参与
            4. 设计适当的表扬和鼓励语句
            
            【格式规范】：
            1. 使用@符号引入音乐、图片或视频（如@过渡.mp3）
            2. 使用#符号表示机器人动作（如#摇头、#呲牙）
            3. 使用{{{{点名}}}}进行简单的人机交互
            4. 使用+N表示停顿N秒
            5. 使用=符号开头的命令进行机器人交互，请注意以下交互格式要求：
               - 选择题格式：=选择 问题@选项1.png @选项2.png ...
               - 不重复选择格式：=不重复选择 {{ =选项1@图片1.png 词条1 ... }}
               - 重复选择格式：=重复选择*N {{ 内容 }}
               - 连线格式：=连线 标题 {{ =配对1@图片1.png@图片2.png ... }}
               - 多选格式：=多选 问题 {{ =正确选项1@图片1.png =错误选项1@图片3.png ... }}
               - 展示格式：=展示 说明文字
            
            确保生成的课件文本：
            1. 结构清晰，有明确的开始、发展和结束部分
            2. 交互元素自然融入教学流程
            3. 符合机器人识别和播放的技术要求
            4. 教学内容、文字旁白和交互设计保持一致性
            5. 所有交互元素（除选择题外）都使用大括号包围选项
            6. 包含足够的文字旁白和引导，增强教学效果
            7. 适当使用机器人动作增加生动性
            
            重要：最终输出的是纯文本文件，不需要Markdown格式
            """,
            expected_output="一份完整的课件文本，符合机器人（App）识别的格式，包含教学内容、文字旁白、机器人动作和交互元素。",
            agent=self.content_integrator,
            context=[curriculum_task, interaction_task],
            output_file='courseware.txt'
        )
        
        # 媒体处理任务
        media_task = Task(
            description=f"""
            处理课件文本中的媒体内容（图片和视频），生成相应的媒体资源。
            
            【任务说明】：
            1. 分析课件文本中的@符号引入的媒体资源
            2. 根据文件扩展名区分媒体类型（图片或视频）
            3. 为每个媒体资源生成适当的提示词
            4. 调用相应的生成工具（图片生成或视频生成）
            5. 保存媒体生成结果信息
            
            【图片生成提示词优化建议】：
            1. 提示词结构指南：
               - 当需要更准确的描述词响应时：主体描述+风格+美学
               - 当需要更高的美学表现时：风格+主体描述+美学+氛围
            2. 使用专业的短词语形容风格、镜头语言等美学描述
            3. 使用自然语言完整连贯地描述画面的主体描述(主体+行为+环境)
            4. 把要重点突出的内容放置在最前面
            5. 用正向提示词替换负面描述词
            6. 通过明确"文字"生成来强调,帮助模型理解
            7. 增加详细的位置、风格描述,使文字生成更可控
            8. 对于复杂内容可以反复强调
            
            【视频生成提示词优化建议】：
            1. 明确运镜方式 - 描述镜头的动态和视觉焦点
            2. 明确行动逻辑 - 描述连续动作的顺序和逻辑
            3. 匹配多镜头组合 - 给每个镜头编号并匹配描述
            4. 文字描述精准 - 使用精确词汇传达想法，避免含糊不清
            
            请确保生成的媒体内容适合小学教育使用，内容健康、积极、有教育意义。
            """,
            expected_output="一份媒体处理报告，包含所有提取的媒体信息和生成结果，以JSON格式保存。",
            agent=self.content_integrator,
            context=[content_task]
        )
        
        return [curriculum_task, interaction_task, content_task, media_task]
    
    def crew(self):
        """创建空crew，在kickoff时再添加任务"""
        return Crew(
            agents=[
                self.curriculum_designer,
                self.interactive_designer,
                self.content_integrator
            ],
            tasks=[],  # 先创建空任务列表，在kickoff时添加
            process=Process.sequential,
            verbose=True
        )
    
    def kickoff(self, inputs=None):
        """启动crew任务"""
        if inputs is None:
            inputs = {}
        
        # 获取输入参数
        subject = inputs.get('subject', '语文')
        grade = inputs.get('grade', '一年级')
        textbook_version = inputs.get('textbook_version', '人教版')
        unit = inputs.get('unit', '礼貌用语')
        current_year = inputs.get('current_year', '2025')
        
        # 创建任务
        tasks = self.create_tasks(subject, grade, textbook_version, unit, current_year)
        
        # 创建crew
        crew = Crew(
            agents=[
                self.curriculum_designer,
                self.interactive_designer,
                self.content_integrator
            ],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
        
        # 启动crew
        return crew.kickoff()
